import mongoose from 'mongoose';

const reviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
  },
  rating: {
    type: Number,
    required: [true, 'Please provide a rating'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5'],
  },
  title: {
    type: String,
    required: [true, 'Please provide a review title'],
    trim: true,
    maxlength: [100, 'Review title cannot exceed 100 characters'],
  },
  comment: {
    type: String,
    required: [true, 'Please provide a review comment'],
    trim: true,
    maxlength: [1000, 'Review comment cannot exceed 1000 characters'],
  },
  images: [{
    public_id: String,
    url: String,
  }],
  verified: {
    type: Boolean,
    default: false, // Set to true if user has purchased the product
  },
  helpful: {
    type: Number,
    default: 0,
    min: [0, 'Helpful count cannot be negative'],
  },
  helpfulVotes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    vote: {
      type: String,
      enum: ['helpful', 'not_helpful'],
    },
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  moderationStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'approved',
  },
  moderationNotes: String,
}, {
  timestamps: true,
});

// Ensure one review per user per product
reviewSchema.index({ user: 1, product: 1 }, { unique: true });

// Update product rating after review is saved
reviewSchema.post('save', async function() {
  const Product = mongoose.model('Product');
  const product = await Product.findById(this.product);
  if (product) {
    await product.calculateAverageRating();
  }
});

// Update product rating after review is removed
reviewSchema.post('remove', async function() {
  const Product = mongoose.model('Product');
  const product = await Product.findById(this.product);
  if (product) {
    await product.calculateAverageRating();
  }
});

// Method to check if user found review helpful
reviewSchema.methods.isHelpfulToUser = function(userId) {
  return this.helpfulVotes.some(vote => 
    vote.user.toString() === userId.toString() && vote.vote === 'helpful'
  );
};

// Method to add helpful vote
reviewSchema.methods.addHelpfulVote = function(userId, voteType) {
  // Remove existing vote from this user
  this.helpfulVotes = this.helpfulVotes.filter(vote => 
    vote.user.toString() !== userId.toString()
  );
  
  // Add new vote
  this.helpfulVotes.push({ user: userId, vote: voteType });
  
  // Update helpful count
  this.helpful = this.helpfulVotes.filter(vote => vote.vote === 'helpful').length;
  
  return this.save();
};

export default mongoose.model('Review', reviewSchema);
