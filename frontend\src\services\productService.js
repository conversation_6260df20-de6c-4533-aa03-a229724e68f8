import api from './api';

const API_URL = '/products';

// Get all products
const getProducts = async (params = {}) => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`${API_URL}?${queryString}`);
  return response.data;
};

// Get single product
const getProduct = async (productId) => {
  const response = await api.get(`${API_URL}/${productId}`);
  return response.data;
};

// Get featured products
const getFeaturedProducts = async () => {
  const response = await api.get(`${API_URL}/featured`);
  return response.data;
};

// Get categories
const getCategories = async () => {
  const response = await api.get(`${API_URL}/categories`);
  return response.data;
};

// Get brands
const getBrands = async () => {
  const response = await api.get(`${API_URL}/brands`);
  return response.data;
};

// Create product (Admin)
const createProduct = async (productData, token) => {
  const response = await api.post(API_URL, productData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Update product (Admin)
const updateProduct = async (productId, productData, token) => {
  const response = await api.put(`${API_URL}/${productId}`, productData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Delete product (Admin)
const deleteProduct = async (productId, token) => {
  const response = await api.delete(`${API_URL}/${productId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const productService = {
  getProducts,
  getProduct,
  getFeaturedProducts,
  getCategories,
  getBrands,
  createProduct,
  updateProduct,
  deleteProduct,
};

export default productService;
