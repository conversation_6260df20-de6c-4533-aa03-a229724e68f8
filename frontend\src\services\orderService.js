import api from './api';

const API_URL = '/orders';

// Create order
const createOrder = async (orderData, token) => {
  const response = await api.post(API_URL, orderData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Get user orders
const getMyOrders = async (params = {}, token) => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`${API_URL}/myorders?${queryString}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Get single order
const getOrder = async (orderId, token) => {
  const response = await api.get(`${API_URL}/${orderId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Cancel order
const cancelOrder = async (orderId, reason, token) => {
  const response = await api.put(`${API_URL}/${orderId}/cancel`, { reason }, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Get all orders (Admin)
const getAllOrders = async (params = {}, token) => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`${API_URL}?${queryString}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Update order status (Admin)
const updateOrderStatus = async (orderId, status, token) => {
  const response = await api.put(`${API_URL}/${orderId}/status`, { status }, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const orderService = {
  createOrder,
  getMyOrders,
  getOrder,
  cancelOrder,
  getAllOrders,
  updateOrderStatus,
};

export default orderService;
