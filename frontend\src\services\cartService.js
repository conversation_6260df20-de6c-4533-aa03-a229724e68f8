import api from './api';

const API_URL = '/cart';

// Get cart
const getCart = async (token) => {
  const response = await api.get(API_URL, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Add to cart
const addToCart = async (itemData, token) => {
  const response = await api.post(`${API_URL}/add`, itemData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Update cart item
const updateCartItem = async (itemData, token) => {
  const response = await api.put(`${API_URL}/update`, itemData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Remove from cart
const removeFromCart = async (productId, token) => {
  const response = await api.delete(`${API_URL}/remove/${productId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// Clear cart
const clearCart = async (token) => {
  const response = await api.delete(`${API_URL}/clear`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const cartService = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
};

export default cartService;
