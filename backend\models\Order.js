import mongoose from 'mongoose';

const orderSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  orderItems: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    quantity: {
      type: Number,
      required: true,
      min: [1, 'Quantity must be at least 1'],
    },
    image: {
      type: String,
      required: true,
    },
    price: {
      type: Number,
      required: true,
      min: [0, 'Price cannot be negative'],
    },
  }],
  shippingAddress: {
    name: {
      type: String,
      required: true,
    },
    phone: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    city: {
      type: String,
      required: true,
    },
    state: {
      type: String,
      required: true,
    },
    country: {
      type: String,
      required: true,
    },
    zipCode: {
      type: String,
      required: true,
    },
  },
  paymentInfo: {
    id: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: ['card', 'paypal', 'bank_transfer'],
    },
  },
  itemsPrice: {
    type: Number,
    required: true,
    default: 0.0,
    min: [0, 'Items price cannot be negative'],
  },
  taxPrice: {
    type: Number,
    required: true,
    default: 0.0,
    min: [0, 'Tax price cannot be negative'],
  },
  shippingPrice: {
    type: Number,
    required: true,
    default: 0.0,
    min: [0, 'Shipping price cannot be negative'],
  },
  totalPrice: {
    type: Number,
    required: true,
    default: 0.0,
    min: [0, 'Total price cannot be negative'],
  },
  orderStatus: {
    type: String,
    required: true,
    default: 'Processing',
    enum: [
      'Processing',
      'Confirmed',
      'Shipped',
      'Out for Delivery',
      'Delivered',
      'Cancelled',
      'Returned',
      'Refunded',
    ],
  },
  deliveredAt: Date,
  shippedAt: Date,
  trackingNumber: String,
  estimatedDelivery: Date,
  orderNotes: String,
  cancellationReason: String,
  refundAmount: {
    type: Number,
    default: 0,
    min: [0, 'Refund amount cannot be negative'],
  },
  refundStatus: {
    type: String,
    enum: ['none', 'pending', 'processed', 'failed'],
    default: 'none',
  },
}, {
  timestamps: true,
});

// Calculate order totals before saving
orderSchema.pre('save', function(next) {
  // Calculate items price
  this.itemsPrice = this.orderItems.reduce((acc, item) => {
    return acc + (item.price * item.quantity);
  }, 0);

  // Calculate tax (assuming 10% tax rate)
  this.taxPrice = Number((this.itemsPrice * 0.1).toFixed(2));

  // Calculate total price
  this.totalPrice = Number((this.itemsPrice + this.taxPrice + this.shippingPrice).toFixed(2));

  next();
});

// Update order status with timestamp
orderSchema.methods.updateStatus = function(status) {
  this.orderStatus = status;
  
  switch (status) {
    case 'Shipped':
      this.shippedAt = new Date();
      break;
    case 'Delivered':
      this.deliveredAt = new Date();
      break;
  }
  
  return this.save();
};

// Check if order can be cancelled
orderSchema.methods.canBeCancelled = function() {
  return ['Processing', 'Confirmed'].includes(this.orderStatus);
};

// Check if order can be returned
orderSchema.methods.canBeReturned = function() {
  if (this.orderStatus !== 'Delivered' || !this.deliveredAt) {
    return false;
  }
  
  // Allow returns within 30 days of delivery
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  return this.deliveredAt > thirtyDaysAgo;
};

export default mongoose.model('Order', orderSchema);
