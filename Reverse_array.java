
//? Using temp array
class Solution {
    public void reverseArray(int arr[]) {
        int[] temp = new int[arr.length];
        
        for (int i = 0; i < arr.length; i++) {
            temp[i] = arr[arr.length - 1 - i];
        }
        
        for (int i = 0; i < arr.length; i++) {
            arr[i] = temp[i];
        }
    }
}

//? Tow Pointer Approach 
class Solution {
    public void reverseArray(int arr[]) {
        int start = 0; 
        int end = arr.length - 1;
        
        while (start < end) {
            int temp = arr[start];
            arr[start] = arr[end];
            arr[end] = temp;
            
        start++;
        end--;
        }
    }
}

