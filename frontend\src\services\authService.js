import api from './api';

const API_URL = '/auth';

// Register user
const register = async (userData) => {
  const response = await api.post(`${API_URL}/register`, userData);

  if (response.data) {
    localStorage.setItem('user', JSON.stringify(response.data.user));
    localStorage.setItem('token', response.data.token);
  }

  return response.data;
};

// Login user
const login = async (userData) => {
  const response = await api.post(`${API_URL}/login`, userData);

  if (response.data) {
    localStorage.setItem('user', JSON.stringify(response.data.user));
    localStorage.setItem('token', response.data.token);
  }

  return response.data;
};

// Logout user
const logout = () => {
  localStorage.removeItem('user');
  localStorage.removeItem('token');
  return api.get(`${API_URL}/logout`);
};

// Get user profile
const getProfile = async (token) => {
  const response = await api.get(`${API_URL}/me`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
};

// Update user profile
const updateProfile = async (userData, token) => {
  const response = await api.put(`${API_URL}/updatedetails`, userData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (response.data) {
    localStorage.setItem('user', JSON.stringify(response.data.user));
  }

  return response.data;
};

// Update password
const updatePassword = async (passwordData, token) => {
  const response = await api.put(`${API_URL}/updatepassword`, passwordData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (response.data) {
    localStorage.setItem('user', JSON.stringify(response.data.user));
    localStorage.setItem('token', response.data.token);
  }

  return response.data;
};

// Forgot password
const forgotPassword = async (email) => {
  const response = await api.post(`${API_URL}/forgotpassword`, { email });
  return response.data;
};

// Reset password
const resetPassword = async (token, password) => {
  const response = await api.put(`${API_URL}/resetpassword/${token}`, { password });

  if (response.data) {
    localStorage.setItem('user', JSON.stringify(response.data.user));
    localStorage.setItem('token', response.data.token);
  }

  return response.data;
};

const authService = {
  register,
  login,
  logout,
  getProfile,
  updateProfile,
  updatePassword,
  forgotPassword,
  resetPassword,
};

export default authService;
